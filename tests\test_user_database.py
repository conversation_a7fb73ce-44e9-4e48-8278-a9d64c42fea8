import shutil
import tempfile
import pytest
from tomsze_utils.auth_utils import create_access_token
from tomsze_utils.env_parser_utils import parse_env
from tomsze_utils.database_utils import user_database
from tomsze_utils.database_utils.user_database import ResponseKeys, UserDatabase
from tomsze_utils.database_utils.user_database_constants import (
    Roles,
    ErrorMessages,
    SuccessMessages,
)


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


def assert_api_response_format(response, expected_success=None, expected_message=None):
    """Assert that response follows ApiResponse format."""
    # Check that response has the correct structure
    assert isinstance(
        response, dict
    ), f"Response should be a dict, got {type(response)}"

    # Check required keys
    assert "is_success" in response, "Response must have 'is_success' key"
    assert "message" in response, "Response must have 'message' key"

    # Check that old format keys are NOT present
    assert "db_result" not in response, "Response should not have old 'db_result' key"

    # Check expected values if provided
    if expected_success is not None:
        assert (
            response["is_success"] == expected_success
        ), f"Expected is_success={expected_success}, got {response['is_success']}"

    if expected_message is not None:
        assert (
            response["message"] == expected_message
        ), f"Expected message='{expected_message}', got '{response['message']}'"


class TestUserDatabase:
    @pytest.fixture
    def user_db(self, temp_dir_path):
        return UserDatabase(db_fpath=temp_dir_path)

    class TestSendVerificationEmail:
        def test_send_verification_email_success(self, user_db: UserDatabase):
            env_dict = parse_env("./.env_email")

            response = user_db.send_verification_email(
                env_dict["RECEIVER_EMAIL"],
                is_really_send_email=False,
            )
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.VERIFICATION_EMAIL_SENT,
            )

    class TestSignUp:
        def test_sign_up_success(self, user_db: UserDatabase):
            response = user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "Password123!", verification_code
            )
            assert response["is_success"] is True
            assert response["message"] == "Sign up successful."

        def test_sign_up_already_registered(self, user_db: UserDatabase):
            response = user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")
            user_db.verify_user_email(email="<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "Password123!", verification_code
            )

            assert response["is_success"] is False
            assert response["message"] == "Email already registered."

    class TestSignIn:
        def test_sign_in_success(self, user_db: UserDatabase):
            response = user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "Password123!", verification_code
            )

            response = user_db.sign_in("<EMAIL>", "Password123!")
            assert response["db_result"]["success"] is True
            assert response["db_result"]["message"] == "Sign in successful."

        def test_sign_in_user_not_found(self, user_db: UserDatabase):
            response = user_db.sign_in("<EMAIL>", "Password123!")
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "User not found."

        def test_sign_in_incorrect_password(self, user_db: UserDatabase):
            response = user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "correct_pass", verification_code
            )

            response = user_db.sign_in("<EMAIL>", "wrong_pass")
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "Incorrect password."

        def test_sign_in_with_access_token_success(self, user_db: UserDatabase):
            # Sign up a user first
            response = user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "Password123!", verification_code
            )

            # Sign in to get an access token
            sign_in_response = user_db.sign_in("<EMAIL>", "Password123!")
            access_token = sign_in_response["db_result"]["access_token"]

            # Sign in with the access token
            response = user_db.sign_in_with_access_token(access_token)
            assert response["db_result"]["success"] is True
            assert response["db_result"]["message"] == "User logged in."
            assert "access_token" in response["db_result"]

        def test_sign_in_with_access_token_invalid_token(self, user_db: UserDatabase):
            response = user_db.sign_in_with_access_token("invalid_token")
            assert response["db_result"]["success"] is False
            assert "Invalid token" in response["db_result"]["message"]

        def test_sign_in_with_access_token_user_not_found(self, user_db: UserDatabase):
            # Generate a token for a non-existent user
            payload = {"email": "<EMAIL>"}
            access_token = create_access_token(data=payload)

            # Attempt to sign in with the token
            response = user_db.sign_in_with_access_token(access_token)
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "User not found."

    class TestIsUserEmailVerified:
        def test_is_user_email_verified_success(self, user_db: UserDatabase):
            user_db.sign_up("<EMAIL>", "Password123!")
            response = user_db.is_user_email_verified("<EMAIL>")
            assert response["db_result"]["is_verified"] is False
            assert response["db_result"]["message"] == "Verification status retrieved."

        def test_is_user_email_verified_user_not_found(self, user_db: UserDatabase):
            response = user_db.is_user_email_verified("<EMAIL>")
            assert response["db_result"]["is_verified"] is False
            assert response["db_result"]["message"] == "User not found."

    class TestVerifyUserEmail:
        def test_verify_user_email_success(self, user_db: UserDatabase):
            user_db.sign_up("<EMAIL>", "Password123!")
            response = user_db.verify_user_email("<EMAIL>")
            assert response["db_result"]["success"] is True
            assert response["db_result"]["message"] == "Email verified."

        def test_verify_user_email_user_not_found(self, user_db: UserDatabase):
            response = user_db.verify_user_email("<EMAIL>")
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "User not found."

    class TestRequestPasswordReset:
        def test_request_password_reset_success(self, user_db: UserDatabase):
            user_db.sign_up("<EMAIL>", "Password123!")
            response = user_db.request_password_reset("<EMAIL>")
            assert response["db_result"]["success"] is True
            assert response["db_result"]["message"] == "Password reset email sent."

        def test_request_password_reset_user_not_found(self, user_db: UserDatabase):
            response = user_db.request_password_reset("<EMAIL>")
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "User not found."

        def test_reset_password_success(self, user_db: UserDatabase):
            user_db.sign_up("<EMAIL>", "Password123!")
            user_db.request_password_reset("<EMAIL>")
            reset_token = user_db.db.query_key("<EMAIL>")["reset_token"]
            response = user_db.reset_password(reset_token, "NewPassword123!")
            assert response["db_result"]["success"] is True

    class TestResetPassword:
        def test_reset_password_invalid_token(self, user_db: UserDatabase):
            user_db.sign_up("<EMAIL>", "Password123!")
            response = user_db.reset_password("invalid_token", "NewPassword123!")
            assert response["db_result"]["success"] is False

    class TestGetNumberOfUsers:
        def test_get_number_of_users(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            response = user_db.sign_up(
                "<EMAIL>", "Password123!", verification_code
            )

            # Get the number of users
            num_users = user_db.get_number_of_users()

            # Assert that the number of users is correct
            assert num_users == 2

        def test_get_number_of_users_empty_db(self, user_db: UserDatabase):
            # Get the number of users when the database is empty
            num_users = user_db.get_number_of_users()

            # Assert that the number of users is 0
            assert num_users == 0

    class TestGetNumFreeUsers:
        def test_get_num_free_users(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            num_free_users = user_db.get_num_free_users()
            expect = 1
            assert num_free_users == expect

    class TestGetNumPaidUsers:
        def test_get_num_paid_users(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            user_db.upgrade_user_to_paid_plan("<EMAIL>")

            num_paid_users = user_db.get_num_paid_users()
            expect = 1
            assert num_paid_users == expect

    class TestGetNumLoggedInUsers:
        def test_get_num_logged_in_users(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            user_db.set_user_to_logged_in("<EMAIL>")

            num_logged_in_users = user_db.get_num_logged_in_users()
            expect = 1
            assert num_logged_in_users == expect

    class TestSetAndGetUserRole:
        def test_set_and_get_user_role(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            user_db.set_user_role("<EMAIL>", "admin")

            response = user_db.get_user_role("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.ROLE_RETRIEVED,
            )
            user_role = response["data"]["role"]
            expect = "admin"
            assert user_role == expect

    class TestSetAndGetUserUseCount:
        def test_set_user_use_count_success(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            response = user_db.set_user_use_count("<EMAIL>", 10)
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.USE_COUNT_UPDATED,
            )

        def test_set_user_use_count_user_not_found(self, user_db: UserDatabase):
            response = user_db.set_user_use_count("<EMAIL>", 10)
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

    class TestGetUserUseCount:
        def test_get_user_use_count_success(self, user_db: UserDatabase):
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            user_db.set_user_use_count("<EMAIL>", 10)
            response = user_db.get_user_use_count("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.USE_COUNT_RETRIEVED,
            )
            assert response["data"]["use_count"] == 10

        def test_get_user_use_count_user_not_found(self, user_db: UserDatabase):
            response = user_db.get_user_use_count("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

    class TestCreateTokensByRefreshToken:
        def test_create_tokens_by_refresh_token_success(self, user_db: UserDatabase):
            # Sign up a user first
            user_db.send_verification_email(
                "<EMAIL>",
                is_really_send_email=False,
                store_user_data=True,
            )

            verification_code = user_db._get_user_verification_code("<EMAIL>")

            user_db.sign_up("<EMAIL>", "Password123!", verification_code)

            # Sign in to get a refresh token
            sign_in_response = user_db.sign_in("<EMAIL>", "Password123!")
            refresh_token = sign_in_response["db_result"]["refresh_token"]

            # Create new tokens using the refresh token
            token_response = user_db.create_tokens_by_refresh_token(
                "<EMAIL>", refresh_token
            )
            assert token_response["db_result"]["success"] is True
            assert "access_token" in token_response["db_result"]
            assert "refresh_token" in token_response["db_result"]

        def test_create_tokens_by_refresh_token_invalid_token(
            self, user_db: UserDatabase
        ):
            # Create new tokens using an invalid refresh token
            token_response = user_db.create_tokens_by_refresh_token(
                "<EMAIL>", "invalid_refresh_token"
            )
            assert token_response["db_result"]["success"] is False
            assert "Invalid token" in token_response["db_result"]["message"]

    class TestGetVerificationCode:
        def test_obtain_verification_code_new_user(self, user_db: UserDatabase):
            email = "<EMAIL>"
            password = "Password123!"
            response = user_db.obtain_verification_code(email, password)

            assert response["db_result"]["success"] is True
            assert "verification_code" in response["db_result"]

            # Verify that the user is stored with is_verified=False
            user_data = user_db.db.query_key(email, default=None)
            assert user_data is not None
            assert user_data["is_verified"] is False

        def test_obtain_verification_code_existing_user_not_verified(
            self, user_db: UserDatabase
        ):
            email = "<EMAIL>"
            password = "Password123!"

            # First, create the user with is_verified=False
            user_db._add_or_set_user_to_db(email, password, is_verified=False)

            # Get the verification code
            response = user_db.obtain_verification_code(email, password)

            assert response["db_result"]["success"] is True
            assert "verification_code" in response["db_result"]

            # Verify that a new verification code is generated
            user_data = user_db.db.query_key(email, default=None)
            assert user_data is not None
            # assert user_data["verification_code"] == response["db_result"]["verification_code"]

        def test_obtain_verification_code_existing_user_already_verified(
            self, user_db: UserDatabase
        ):
            email = "<EMAIL>"
            password = "Password123!"

            # First, create and verify the user
            user_db._add_or_set_user_to_db(email, password, is_verified=True)

            # Try to get a verification code again
            response = user_db.obtain_verification_code(email, password)
            assert response["db_result"]["success"] is False
            assert response["db_result"]["message"] == "User already registered."

    class TestMergeResponses:
        def test_merge_responses_success(self, user_db: UserDatabase):
            # Test with new ApiResponse format
            response_list = [
                {
                    "is_success": True,
                    "message": "Success message 1.",
                    "data": {"val1": "1"},
                },
                {
                    "is_success": False,
                    "message": "Success message 2.",
                    "data": {"val2": "2"},
                },
            ]
            merged_response = user_db.merge_responses(response_list)

            # Should return error response since one response failed
            assert_api_response_format(merged_response, expected_success=False)
            assert "Success message 1. Success message 2." in merged_response["message"]
            assert merged_response["error"]["val1"] == "1"
            assert merged_response["error"]["val2"] == "2"

    class TestApiResponseFormat:
        """Test that functions return ApiResponse format instead of old format."""

        def test_get_all_users_api_response_format(self, user_db: UserDatabase):
            """Test get_all_users returns correct ApiResponse format."""
            response = user_db.get_all_users()
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.USERS_RETRIEVED,
            )
            assert "data" in response, "get_all_users should have 'data' key"
            assert (
                "users" in response["data"]
            ), "get_all_users data should contain 'users' key"

        def test_get_user_role_api_response_format_user_not_found(
            self, user_db: UserDatabase
        ):
            """Test get_user_role returns correct ApiResponse format for non-existent user."""
            response = user_db.get_user_role("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

        def test_set_user_role_api_response_format_user_not_found(
            self, user_db: UserDatabase
        ):
            """Test set_user_role returns correct ApiResponse format for non-existent user."""
            response = user_db.set_user_role("<EMAIL>", Roles.USER)
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

        def test_validate_access_token_api_response_format_invalid_token(
            self, user_db: UserDatabase
        ):
            """Test validate_access_token returns correct ApiResponse format for invalid token."""
            response = user_db.validate_access_token("invalid_token")
            assert_api_response_format(response, expected_success=False)
            # Message could be various token error messages, so we don't check exact message

        def test_upgrade_user_to_paid_plan_api_response_format_user_not_found(
            self, user_db: UserDatabase
        ):
            """Test upgrade_user_to_paid_plan returns correct ApiResponse format for non-existent user."""
            response = user_db.upgrade_user_to_paid_plan("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

        def test_obtain_verification_code_api_response_format_new_user(
            self, user_db: UserDatabase
        ):
            """Test obtain_verification_code returns correct ApiResponse format for new user."""
            response = user_db.obtain_verification_code(
                "<EMAIL>", store_user_data=False
            )
            assert_api_response_format(
                response,
                expected_success=True,
                expected_message=SuccessMessages.GENERATED_VERIFICATION_CODE,
            )
            assert "data" in response, "obtain_verification_code should have 'data' key"
            assert (
                "verification_code" in response["data"]
            ), "obtain_verification_code data should contain 'verification_code' key"

        def test_set_user_use_count_api_response_format_user_not_found(
            self, user_db: UserDatabase
        ):
            """Test set_user_use_count returns correct ApiResponse format for non-existent user."""
            response = user_db.set_user_use_count("<EMAIL>", 10)
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )

        def test_get_user_use_count_api_response_format_user_not_found(
            self, user_db: UserDatabase
        ):
            """Test get_user_use_count returns correct ApiResponse format for non-existent user."""
            response = user_db.get_user_use_count("<EMAIL>")
            assert_api_response_format(
                response,
                expected_success=False,
                expected_message=ErrorMessages.USER_NOT_FOUND,
            )
